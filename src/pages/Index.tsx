import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Mic, Book, ArrowUp, Pause, Play, Square } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import VoiceInput from '@/components/VoiceInput';
import ChatMessage from '@/components/ChatMessage';
import ScriptureCard from '@/components/ScriptureCard';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  scripture?: ScripturePassage[];
}

interface ScripturePassage {
  faith: string;
  book: string;
  chapter: number;
  verse: string;
  text: string;
  citation: string;
}

const Index = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Peace be with you. I am <PERSON><PERSON>, your interfaith scripture companion. Ask me about guidance from the world\'s sacred texts, and I will share wisdom from different traditions to illuminate your path.',
      timestamp: new Date(),
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [canResume, setCanResume] = useState(false);
  const currentUtteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Mock scripture database for demo
  const mockScriptures: ScripturePassage[] = [
    {
      faith: 'Christianity',
      book: 'Matthew',
      chapter: 7,
      verse: '7',
      text: 'Ask and it will be given to you; seek and you will find; knock and the door will be opened to you.',
      citation: 'Christianity - Matthew 7:7'
    },
    {
      faith: 'Islam',
      book: 'Quran',
      chapter: 2,
      verse: '186',
      text: 'And when My servants ask you concerning Me, indeed I am near. I respond to the invocation of the supplicant when he calls upon Me.',
      citation: 'Islam - Quran 2:186'
    },
    {
      faith: 'Buddhism',
      book: 'Dhammapada',
      chapter: 1,
      verse: '1',
      text: 'All that we are is the result of what we have thought. The mind is everything. What we think we become.',
      citation: 'Buddhism - Dhammapada 1:1'
    },
    {
      faith: 'Hinduism',
      book: 'Bhagavad Gita',
      chapter: 2,
      verse: '47',
      text: 'You have a right to perform your prescribed duty, but not to the fruits of actions. Never consider yourself the cause of the results of your activities.',
      citation: 'Hinduism - Bhagavad Gita 2:47'
    }
  ];

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Parse verse references to detect specific books/chapters
  const parseScriptureReference = (text: string): { book: string; chapter?: number; type: 'book' | 'chapter' | 'verse' } | null => {
    const lowerText = text.toLowerCase();

    // Bible book patterns
    const bookPatterns = [
      // Old Testament
      { pattern: /\b(genesis|gen)\b/i, book: 'Genesis' },
      { pattern: /\b(exodus|exod|ex)\b/i, book: 'Exodus' },
      { pattern: /\b(leviticus|lev)\b/i, book: 'Leviticus' },
      { pattern: /\b(numbers|num)\b/i, book: 'Numbers' },
      { pattern: /\b(deuteronomy|deut|dt)\b/i, book: 'Deuteronomy' },
      { pattern: /\b(joshua|josh)\b/i, book: 'Joshua' },
      { pattern: /\b(judges|judg)\b/i, book: 'Judges' },
      { pattern: /\b(ruth)\b/i, book: 'Ruth' },
      { pattern: /\b(1\s*samuel|1\s*sam)\b/i, book: '1 Samuel' },
      { pattern: /\b(2\s*samuel|2\s*sam)\b/i, book: '2 Samuel' },
      { pattern: /\b(1\s*kings|1\s*kgs)\b/i, book: '1 Kings' },
      { pattern: /\b(2\s*kings|2\s*kgs)\b/i, book: '2 Kings' },
      { pattern: /\b(psalms?|ps)\b/i, book: 'Psalms' },
      { pattern: /\b(proverbs|prov)\b/i, book: 'Proverbs' },
      { pattern: /\b(ecclesiastes|eccl)\b/i, book: 'Ecclesiastes' },
      { pattern: /\b(isaiah|isa)\b/i, book: 'Isaiah' },
      { pattern: /\b(jeremiah|jer)\b/i, book: 'Jeremiah' },
      { pattern: /\b(daniel|dan)\b/i, book: 'Daniel' },
      // New Testament
      { pattern: /\b(matthew|matt|mt)\b/i, book: 'Matthew' },
      { pattern: /\b(mark|mk)\b/i, book: 'Mark' },
      { pattern: /\b(luke|lk)\b/i, book: 'Luke' },
      { pattern: /\b(john|jn)\b/i, book: 'John' },
      { pattern: /\b(acts)\b/i, book: 'Acts' },
      { pattern: /\b(romans|rom)\b/i, book: 'Romans' },
      { pattern: /\b(1\s*corinthians|1\s*cor)\b/i, book: '1 Corinthians' },
      { pattern: /\b(2\s*corinthians|2\s*cor)\b/i, book: '2 Corinthians' },
      { pattern: /\b(galatians|gal)\b/i, book: 'Galatians' },
      { pattern: /\b(ephesians|eph)\b/i, book: 'Ephesians' },
      { pattern: /\b(philippians|phil)\b/i, book: 'Philippians' },
      { pattern: /\b(colossians|col)\b/i, book: 'Colossians' },
      { pattern: /\b(1\s*thessalonians|1\s*thess)\b/i, book: '1 Thessalonians' },
      { pattern: /\b(2\s*thessalonians|2\s*thess)\b/i, book: '2 Thessalonians' },
      { pattern: /\b(1\s*timothy|1\s*tim)\b/i, book: '1 Timothy' },
      { pattern: /\b(2\s*timothy|2\s*tim)\b/i, book: '2 Timothy' },
      { pattern: /\b(titus|tit)\b/i, book: 'Titus' },
      { pattern: /\b(philemon|phlm)\b/i, book: 'Philemon' },
      { pattern: /\b(hebrews|heb)\b/i, book: 'Hebrews' },
      { pattern: /\b(james|jas)\b/i, book: 'James' },
      { pattern: /\b(1\s*peter|1\s*pet)\b/i, book: '1 Peter' },
      { pattern: /\b(2\s*peter|2\s*pet)\b/i, book: '2 Peter' },
      { pattern: /\b(1\s*john|1\s*jn)\b/i, book: '1 John' },
      { pattern: /\b(2\s*john|2\s*jn)\b/i, book: '2 John' },
      { pattern: /\b(3\s*john|3\s*jn)\b/i, book: '3 John' },
      { pattern: /\b(jude)\b/i, book: 'Jude' },
      { pattern: /\b(revelation|rev)\b/i, book: 'Revelation' }
    ];

    // Check for specific verse patterns first (e.g., "Matthew 7:7")
    const versePattern = /(\w+)\s*(\d+):(\d+)/i;
    const verseMatch = text.match(versePattern);
    if (verseMatch) {
      const bookName = verseMatch[1];
      const chapter = parseInt(verseMatch[2]);
      const verse = parseInt(verseMatch[3]);

      for (const { pattern, book } of bookPatterns) {
        if (pattern.test(bookName)) {
          return { book, chapter, type: 'verse' };
        }
      }
    }

    // Check for chapter patterns (e.g., "Matthew 7")
    const chapterPattern = /(\w+)\s*(\d+)(?!\s*:)/i;
    const chapterMatch = text.match(chapterPattern);
    if (chapterMatch) {
      const bookName = chapterMatch[1];
      const chapter = parseInt(chapterMatch[2]);

      for (const { pattern, book } of bookPatterns) {
        if (pattern.test(bookName)) {
          return { book, chapter, type: 'chapter' };
        }
      }
    }

    // Check for book patterns (e.g., "book of Ruth", "Ruth")
    for (const { pattern, book } of bookPatterns) {
      if (pattern.test(text)) {
        return { book, type: 'book' };
      }
    }

    return null;
  };

  // Generate specific responses for biblical books
  const generateBookResponse = (book: string, chapter?: number): { reflection: string; scriptures: ScripturePassage[] } => {
    const bookResponses: { [key: string]: any } = {
      'Ruth': {
        introduction: "The Book of Ruth is a beautiful story of loyalty, love, and redemption set during the time of the judges.",
        themes: [
          "Loyalty and devotion (Ruth's commitment to Naomi)",
          "God's providence working through ordinary circumstances",
          "Redemption and restoration",
          "The inclusion of foreigners in God's plan",
          "Love that transcends cultural boundaries"
        ],
        reflection: "Ruth's story shows us that faithfulness in small things can lead to extraordinary outcomes. Her famous words 'Where you go I will go, and where you stay I will stay' demonstrate a love that chooses commitment over convenience.",
        application: "Ruth's example challenges us to consider: How do we show loyalty in our relationships? How might God be working through our ordinary circumstances? What does it mean to choose love over self-interest?",
        scriptures: [
          {
            faith: 'Christianity',
            book: 'Ruth',
            chapter: 1,
            verse: '16',
            text: 'But Ruth replied, "Don\'t urge me to leave you or to turn back from you. Where you go I will go, and where you stay I will stay. Your people will be my people and your God my God."',
            citation: 'Ruth 1:16'
          }
        ]
      },
      'Matthew': {
        introduction: "The Gospel of Matthew presents Jesus as the promised Messiah and King, emphasizing His teachings and their practical application.",
        themes: [
          "Jesus as the fulfillment of Old Testament prophecy",
          "The Sermon on the Mount (chapters 5-7)",
          "Parables about the Kingdom of Heaven",
          "Jesus' authority in teaching and miracles",
          "The Great Commission"
        ],
        reflection: "Matthew shows us Jesus not just as Savior, but as Teacher and King. The Gospel emphasizes that following Jesus involves both believing in Him and living according to His teachings.",
        application: "Matthew challenges us to ask: How are we living out Jesus' teachings in our daily lives? What does it mean to be citizens of God's kingdom? How do we share this good news with others?",
        scriptures: [
          {
            faith: 'Christianity',
            book: 'Matthew',
            chapter: 7,
            verse: '12',
            text: 'So in everything, do to others what you would have them do to you, for this sums up the Law and the Prophets.',
            citation: 'Matthew 7:12'
          }
        ]
      },
      'Psalms': {
        introduction: "The Book of Psalms is the prayer book and hymnal of the Bible, expressing the full range of human emotions before God.",
        themes: [
          "Praise and worship of God",
          "Honest expression of doubt, fear, and pain",
          "Trust in God's faithfulness",
          "Wisdom for daily living",
          "Hope in God's deliverance"
        ],
        reflection: "The Psalms teach us that we can bring our whole selves to God - our joys, sorrows, fears, and hopes. They show us that honest prayer is not just acceptable but essential.",
        application: "The Psalms invite us to consider: How honest are we in our prayers? What emotions do we need to bring before God? How can we cultivate a heart of worship in both good times and difficult seasons?",
        scriptures: [
          {
            faith: 'Christianity',
            book: 'Psalms',
            chapter: 23,
            verse: '1',
            text: 'The Lord is my shepherd, I lack nothing.',
            citation: 'Psalm 23:1'
          }
        ]
      }
    };

    const bookData = bookResponses[book];
    if (!bookData) {
      return {
        reflection: `The book of ${book} contains profound wisdom and spiritual insights. I'd love to explore specific passages from this book with you. Could you share a particular chapter or verse that interests you, or let me know what questions you have about this book?`,
        scriptures: []
      };
    }

    let reflection = `**The Book of ${book}**

${bookData.introduction}

**Key Themes:**
${bookData.themes.map((theme: string) => `• ${theme}`).join('\n')}

**Reflection:**
${bookData.reflection}

**Personal Application:**
${bookData.application}

What draws you to the book of ${book}? I'd love to explore specific passages or themes that resonate with you.`;

    if (chapter) {
      reflection = `**${book} Chapter ${chapter}**

${bookData.introduction}

This chapter contains important teachings that speak to the human experience. ${bookData.reflection}

${bookData.application}

What specifically about ${book} chapter ${chapter} interests you? I'd love to dive deeper into the verses that speak to your heart.`;
    }

    return {
      reflection,
      scriptures: bookData.scriptures || []
    };
  };

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: text,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    // Simulate API delay and response
    setTimeout(() => {
      // Check if user is asking about a specific scripture
      const scriptureRef = parseScriptureReference(text);
      let reflection: string;
      let relevantScriptures: ScripturePassage[];

      if (scriptureRef) {
        // Generate specific response for the requested scripture
        const response = generateBookResponse(scriptureRef.book, scriptureRef.chapter);
        reflection = response.reflection;
        relevantScriptures = response.scriptures;
      } else {
        // Default response with general scriptures
        relevantScriptures = mockScriptures.slice(0, 3);
        reflection = `These sacred texts remind us that seeking guidance is a universal human experience. Whether through prayer, meditation, or righteous action, all traditions teach us that sincere seeking opens the heart to divine wisdom. The common thread across these teachings is the importance of both asking and being prepared to receive guidance with an open heart.`;
      }

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: reflection,
        timestamp: new Date(),
        scripture: relevantScriptures,
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);

      // Text-to-speech with pause/resume support
      if ('speechSynthesis' in window) {
        setIsSpeaking(true);
        setIsPaused(false);
        setCanResume(false);

        const utterance = new SpeechSynthesisUtterance(reflection);
        utterance.rate = 0.9;
        utterance.pitch = 1;

        currentUtteranceRef.current = utterance;

        utterance.onstart = () => {
          setIsSpeaking(true);
          setIsPaused(false);
        };

        utterance.onend = () => {
          setIsSpeaking(false);
          setIsPaused(false);
          setCanResume(false);
          currentUtteranceRef.current = null;
        };

        utterance.onerror = () => {
          setIsSpeaking(false);
          setIsPaused(false);
          setCanResume(false);
          currentUtteranceRef.current = null;
        };

        speechSynthesis.speak(utterance);
      }
    }, 1500);
  };

  const handleVoiceInput = (transcript: string) => {
    handleSendMessage(transcript);
  };

  const handleVoiceError = (error: string) => {
    toast({
      title: 'Voice Recognition Error',
      description: error,
      variant: 'destructive',
    });
  };

  // Speech control functions
  const pauseSpeech = () => {
    if ('speechSynthesis' in window && speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
      setIsPaused(true);
      setCanResume(true);
      toast({
        title: 'Speech Paused',
        description: 'You can resume or stop the speech.',
      });
    }
  };

  const resumeSpeech = () => {
    if ('speechSynthesis' in window && speechSynthesis.paused) {
      speechSynthesis.resume();
      setIsPaused(false);
      setCanResume(false);
      toast({
        title: 'Speech Resumed',
        description: 'Continuing where we left off.',
      });
    }
  };

  const stopSpeech = () => {
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
      setIsSpeaking(false);
      setIsPaused(false);
      setCanResume(false);
      currentUtteranceRef.current = null;
      toast({
        title: 'Speech Stopped',
        description: 'Ready for your next question.',
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-wisdom">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-sacred">
              <Book className="h-6 w-6 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-foreground font-interface">
                Logos
              </h1>
              <p className="text-sm text-muted-foreground">
                Interfaith Scripture Companion
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6 max-w-4xl">
        <div className="flex flex-col h-[calc(100vh-200px)]">
          {/* Chat Messages */}
          <ScrollArea className="flex-1 mb-6 chat-scroll" ref={scrollAreaRef}>
            <div className="space-y-6 pr-4">
              {messages.map((message) => (
                <ChatMessage
                  key={message.id}
                  message={message}
                  scriptures={message.scripture}
                />
              ))}
              {isLoading && (
                <div className="flex justify-start">
                  <Card className="p-4 max-w-2xl shadow-wisdom">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
                      <div className="w-2 h-2 bg-accent rounded-full animate-pulse delay-75"></div>
                      <div className="w-2 h-2 bg-accent rounded-full animate-pulse delay-150"></div>
                      <span className="ml-2">Seeking wisdom...</span>
                    </div>
                  </Card>
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Speech Control Buttons */}
          {(isSpeaking || isPaused || canResume) && (
            <div className="flex justify-center gap-3 mb-4">
              {isSpeaking && !isPaused && (
                <Button
                  onClick={pauseSpeech}
                  variant="outline"
                  size="sm"
                  className="bg-yellow-600/20 border-yellow-500/50 text-yellow-300 hover:bg-yellow-600/30 hover:border-yellow-400"
                >
                  <Pause className="h-4 w-4 mr-2" />
                  Pause
                </Button>
              )}

              {isPaused && canResume && (
                <Button
                  onClick={resumeSpeech}
                  variant="outline"
                  size="sm"
                  className="bg-green-600/20 border-green-500/50 text-green-300 hover:bg-green-600/30 hover:border-green-400"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Resume
                </Button>
              )}

              {(isSpeaking || isPaused) && (
                <Button
                  onClick={stopSpeech}
                  variant="outline"
                  size="sm"
                  className="bg-red-600/20 border-red-500/50 text-red-300 hover:bg-red-600/30 hover:border-red-400"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Stop
                </Button>
              )}
            </div>
          )}

          {/* Input Area */}
          <div className="border-t border-border pt-4">
            <Card className="p-4 shadow-sacred">
              <div className="flex gap-3 items-end">
                <div className="flex-1">
                  <Input
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="Ask about wisdom from sacred texts... or use voice"
                    className="resize-none border-muted-foreground/20 focus:border-accent"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage(inputText);
                      }
                    }}
                  />
                </div>
                
                <VoiceInput
                  onTranscript={handleVoiceInput}
                  onError={handleVoiceError}
                  isListening={isListening}
                  setIsListening={setIsListening}
                />

                <Button
                  onClick={() => handleSendMessage(inputText)}
                  disabled={!inputText.trim() || isLoading}
                  className="bg-gradient-sacred hover:shadow-voice transition-all duration-300"
                  size="icon"
                >
                  <ArrowUp className="h-4 w-4" />
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Index;